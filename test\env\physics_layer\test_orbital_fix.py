"""Test script to verify orbital.py fixes"""
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.env.physics_layer.orbital import OrbitalUpdater

def test_orbital_imports():
    """Test if the imports are working correctly"""
    try:
        # Get paths
        current_dir = Path(__file__).parent.parent.parent.parent
        config_file = current_dir / "src" / "env" / "physics_layer" / "config.yaml"
        data_file = current_dir / "src" / "env" / "env_data" / "satellite_data72_1.csv"
        
        # Initialize OrbitalUpdater with explicit paths
        orbital = OrbitalUpdater(
            data_file=str(data_file),
            config_file=str(config_file)
        )
        
        print("[OK] Import and initialization successful")
        print(f"[OK] Config loaded: {orbital.config is not None}")
        print(f"[OK] Satellite data loaded: {not orbital.satellite_data.empty}")
        print(f"[OK] Ground stations loaded: {len(orbital.ground_stations)} stations")
        print(f"[OK] Cloud stations loaded: {len(orbital.cloud_stations)} stations")
        
        # Test a basic operation
        satellites = orbital.get_satellites_at_time(0)
        print(f"[OK] Get satellites at time 0: {len(satellites)} satellites")
        
        # Test type hint fix
        sat_ids = orbital.get_satellite_ids(0)
        print(f"[OK] Get satellite IDs: {len(sat_ids)} IDs, type: {type(sat_ids)}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_orbital_imports()
    if success:
        print("\nAll fixes verified successfully!")
    else:
        print("\nFix verification failed, please check the error messages")