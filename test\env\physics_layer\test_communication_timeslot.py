"""
Communication Module Test - Timeslot-based Data Matrix Output
测试communication_refactored.py模块的通信链路计算功能
按照编程规范要求，输出特定时隙(1-5, 101-105)的完整通信数据矩阵
"""
import sys
import os
from pathlib import Path
import numpy as np
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.env.physics_layer.communication_refactored import CommunicationManager
from src.env.physics_layer.orbital import OrbitalUpdater


def format_matrix_stats(matrix, name, unit=""):
    """格式化输出矩阵统计信息"""
    non_zero = matrix[matrix > 0]
    if len(non_zero) > 0:
        return (f"  {name}: shape={matrix.shape}, "
                f"non-zero={len(non_zero)}, "
                f"mean={np.mean(non_zero):.2f}{unit}, "
                f"max={np.max(non_zero):.2f}{unit}, "
                f"min={np.min(non_zero):.2f}{unit}")
    else:
        return f"  {name}: shape={matrix.shape}, all zeros"


def test_communication_matrices_by_timeslot():
    """
    测试不同时隙的通信矩阵
    输出时隙1-5, 101-105的所有通信相关数据矩阵
    """
    print("=" * 100)
    print("COMMUNICATION MODULE TEST - TIMESLOT-BASED DATA MATRIX OUTPUT")
    print("Testing timeslots: 1-5, 101-105")
    print("=" * 100)
    
    # Initialize Communication Manager
    current_dir = Path(__file__).parent.parent.parent.parent
    config_file = current_dir / "src" / "env" / "physics_layer" / "config.yaml"
    data_file = current_dir / "src" / "env" / "env_data" / "satellite_data72_1.csv"
    
    try:
        # First create orbital updater
        orbital = OrbitalUpdater(
            data_file=str(data_file),
            config_file=str(config_file)
        )
        
        # Then create communication manager
        comm_manager = CommunicationManager(
            orbital_updater=orbital,
            config_file=str(config_file)
        )
        
        print(f"[INIT] CommunicationManager initialized successfully")
        print(f"[INFO] Total timeslots: {orbital.get_total_timeslots()}")
        print(f"[INFO] Number of satellites: {len(orbital.get_satellites_at_time(0))}")
        print(f"[INFO] Number of ground stations: {orbital.get_ground_station_count()}")
        print(f"[INFO] Number of cloud centers: {orbital.get_cloud_station_count()}")
        print()
        
        # Print communication parameters
        print("[COMM PARAMETERS]")
        print(f"  ISL data rate: {comm_manager.isl_data_rate_bps/1e9:.1f} Gbps")
        print(f"  RF carrier frequency: {comm_manager.rf_carrier_freq_hz/1e9:.1f} GHz")
        print(f"  User-Satellite uplink bandwidth: {comm_manager.b_us_hz/1e6:.0f} MHz")
        print(f"  Satellite-User downlink bandwidth: {comm_manager.b_su_hz/1e6:.0f} MHz")
        print(f"  Satellite-Cloud downlink bandwidth: {comm_manager.b_sc_hz/1e6:.0f} MHz")
        print(f"  Cloud-Satellite uplink bandwidth: {comm_manager.b_cs_hz/1e6:.0f} MHz")
        print()
        
    except Exception as e:
        print(f"[ERROR] Failed to initialize: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Test timeslots
    test_timeslots = list(range(1, 6)) + list(range(101, 106))
    
    for timeslot in test_timeslots:
        print("=" * 100)
        print(f"TIMESLOT {timeslot}")
        print("=" * 100)
        
        try:
            # Get time context
            time_context = orbital.time_manager.get_time_context(timeslot)
            print(f"[TIME] Physical time: {time_context.physical_time}")
            print(f"[TIME] Simulation time: {time_context.simulation_time:.2f}s")
            print()
            
            # 1. INTER-SATELLITE LINK (ISL) COMMUNICATION
            print("-" * 50)
            print("1. INTER-SATELLITE LINK (ISL) COMMUNICATION")
            print("-" * 50)
            
            isl_data = comm_manager.get_isl_communication_matrix(timeslot)
            
            print("[ISL Data Matrices]")
            print(format_matrix_stats(isl_data['data_rate_bps'], "Data Rate", " bps"))
            print(format_matrix_stats(isl_data['distance_km'], "Distance", " km"))
            print(format_matrix_stats(isl_data['propagation_delay_ms'], "Propagation Delay", " ms"))
            
            vis_count = np.sum(isl_data['visibility'])
            total_possible = len(isl_data['satellite_ids']) * (len(isl_data['satellite_ids']) - 1)
            print(f"  Visibility: {vis_count}/{total_possible} links visible ({vis_count/total_possible*100:.1f}%)")
            print(f"  Link type: {isl_data['link_type']}")
            
            # Show sample of data rate matrix (first 5x5)
            print("\n  Sample Data Rate Matrix (first 5x5, Gbps):")
            sample_matrix = isl_data['data_rate_bps'][:5, :5] / 1e9
            for row in sample_matrix:
                print("    " + " ".join([f"{val:6.1f}" if val > 0 else "   0.0" for val in row]))
            
            # 2. SATELLITE-GROUND COMMUNICATION
            print("\n" + "-" * 50)
            print("2. SATELLITE-GROUND COMMUNICATION")
            print("-" * 50)
            
            sg_data = comm_manager.get_satellite_ground_communication_matrix(timeslot)
            
            print("[Satellite-Ground Data Matrices]")
            print("Uplink (Ground -> Satellite):")
            print(format_matrix_stats(sg_data['uplink_data_rate_bps']/1e6, "  Data Rate", " Mbps"))
            print(format_matrix_stats(sg_data['uplink_snr_db'], "  SNR", " dB"))
            
            print("Downlink (Satellite -> Ground):")
            print(format_matrix_stats(sg_data['downlink_data_rate_bps']/1e6, "  Data Rate", " Mbps"))
            print(format_matrix_stats(sg_data['downlink_snr_db'], "  SNR", " dB"))
            
            print("Common:")
            print(format_matrix_stats(sg_data['distance_km'], "  Distance", " km"))
            print(format_matrix_stats(sg_data['propagation_delay_ms'], "  Propagation Delay", " ms"))
            
            vis_count = np.sum(sg_data['visibility'])
            total_possible = len(sg_data['satellite_ids']) * len(sg_data['ground_station_ids'])
            print(f"  Visibility: {vis_count}/{total_possible} links visible ({vis_count/total_possible*100:.1f}%)")
            print(f"  Link type: {sg_data['link_type']}")
            
            # Statistics per satellite
            vis_per_sat = np.sum(sg_data['visibility'], axis=1)
            if len(vis_per_sat) > 0:
                print(f"\n  Ground stations visible per satellite:")
                print(f"    Mean: {np.mean(vis_per_sat):.1f}, Max: {np.max(vis_per_sat)}, Min: {np.min(vis_per_sat)}")
            
            # Statistics per ground station
            vis_per_ground = np.sum(sg_data['visibility'], axis=0)
            if len(vis_per_ground) > 0:
                print(f"  Satellites visible per ground station:")
                print(f"    Mean: {np.mean(vis_per_ground):.1f}, Max: {np.max(vis_per_ground)}, Min: {np.min(vis_per_ground)}")
            
            # 3. SATELLITE-CLOUD COMMUNICATION
            print("\n" + "-" * 50)
            print("3. SATELLITE-CLOUD COMMUNICATION")
            print("-" * 50)
            
            sc_data = comm_manager.get_satellite_cloud_communication_matrix(timeslot)
            
            print("[Satellite-Cloud Data Matrices]")
            print("Uplink (Cloud -> Satellite):")
            print(format_matrix_stats(sc_data['uplink_data_rate_bps']/1e6, "  Data Rate", " Mbps"))
            print(format_matrix_stats(sc_data['uplink_snr_db'], "  SNR", " dB"))
            
            print("Downlink (Satellite -> Cloud):")
            print(format_matrix_stats(sc_data['downlink_data_rate_bps']/1e6, "  Data Rate", " Mbps"))
            print(format_matrix_stats(sc_data['downlink_snr_db'], "  SNR", " dB"))
            
            print("Common:")
            print(format_matrix_stats(sc_data['distance_km'], "  Distance", " km"))
            print(format_matrix_stats(sc_data['propagation_delay_ms'], "  Propagation Delay", " ms"))
            
            vis_count = np.sum(sc_data['visibility'])
            total_possible = len(sc_data['satellite_ids']) * len(sc_data['cloud_station_ids'])
            print(f"  Visibility: {vis_count}/{total_possible} links visible ({vis_count/total_possible*100:.1f}%)")
            print(f"  Link type: {sc_data['link_type']}")
            
            # Show full visibility matrix (since it's small - 72x5)
            print("\n  Full Visibility Matrix (72 satellites x 5 cloud centers):")
            print("  Cloud Centers: ", end="")
            for i in range(5):
                print(f"C{i+1:2d} ", end="")    
            print()
            
            # Show first 10 satellites
            for i in range(min(10, sc_data['visibility'].shape[0])):
                print(f"  Sat {i+1:2d}: ", end="")
                for j in range(sc_data['visibility'].shape[1]):
                    print(f"  {int(sc_data['visibility'][i, j])} ", end="")
                print()
            if sc_data['visibility'].shape[0] > 10:
                print("  ... (showing first 10 satellites only)")
            
            # Statistics
            vis_per_sat = np.sum(sc_data['visibility'], axis=1)
            vis_per_cloud = np.sum(sc_data['visibility'], axis=0)
            print(f"\n  Cloud centers visible per satellite:")
            print(f"    Mean: {np.mean(vis_per_sat):.2f}, Max: {np.max(vis_per_sat)}, Min: {np.min(vis_per_sat)}")
            print(f"  Satellites visible per cloud center:")
            for i, count in enumerate(vis_per_cloud):
                print(f"    Cloud {i+1}: {count} satellites")
            
            # 4. LINK QUALITY METRICS SUMMARY
            print("\n" + "-" * 50)
            print("4. LINK QUALITY METRICS SUMMARY")
            print("-" * 50)
            
            metrics = comm_manager.get_link_quality_metrics(timeslot)
            
            print("[ISL Metrics]")
            print(f"  Average data rate: {metrics['isl']['avg_data_rate_gbps']:.2f} Gbps")
            print(f"  Total visible links: {metrics['isl']['total_links']}")
            print(f"  Average propagation delay: {metrics['isl']['avg_delay_ms']:.2f} ms")
            
            print("\n[Satellite-Ground Metrics]")
            print(f"  Average uplink rate: {metrics['satellite_ground']['avg_uplink_rate_mbps']:.2f} Mbps")
            print(f"  Average downlink rate: {metrics['satellite_ground']['avg_downlink_rate_mbps']:.2f} Mbps")
            print(f"  Total visible links: {metrics['satellite_ground']['total_links']}")
            print(f"  Average propagation delay: {metrics['satellite_ground']['avg_delay_ms']:.2f} ms")
            
            print("\n[Satellite-Cloud Metrics]")
            print(f"  Average uplink rate: {metrics['satellite_cloud']['avg_uplink_rate_mbps']:.2f} Mbps")
            print(f"  Average downlink rate: {metrics['satellite_cloud']['avg_downlink_rate_mbps']:.2f} Mbps")
            print(f"  Total visible links: {metrics['satellite_cloud']['total_links']}")
            print(f"  Average propagation delay: {metrics['satellite_cloud']['avg_delay_ms']:.2f} ms")
            
        except Exception as e:
            print(f"[ERROR] Failed to process timeslot {timeslot}: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 100)
    print("TEST COMPLETED")
    print("=" * 100)


def compare_communication_across_timeslots():
    """
    比较不同时隙之间的通信性能变化
    """
    print("\n" + "=" * 100)
    print("COMMUNICATION PERFORMANCE COMPARISON ACROSS TIMESLOTS")
    print("=" * 100)
    
    # Initialize
    current_dir = Path(__file__).parent.parent.parent.parent
    config_file = current_dir / "src" / "env" / "physics_layer" / "config.yaml"
    data_file = current_dir / "src" / "env" / "env_data" / "satellite_data72_1.csv"
    
    try:
        orbital = OrbitalUpdater(
            data_file=str(data_file),
            config_file=str(config_file)
        )
        comm_manager = CommunicationManager(
            orbital_updater=orbital,
            config_file=str(config_file)
        )
    except Exception as e:
        print(f"[ERROR] Failed to initialize: {e}")
        return
    
    # Collect statistics
    early_slots = list(range(1, 6))
    later_slots = list(range(101, 106))
    
    stats = {
        'early': {'isl_links': [], 'sg_links': [], 'sc_links': [], 
                  'sg_uplink': [], 'sg_downlink': [], 'sc_uplink': [], 'sc_downlink': []},
        'later': {'isl_links': [], 'sg_links': [], 'sc_links': [],
                  'sg_uplink': [], 'sg_downlink': [], 'sc_uplink': [], 'sc_downlink': []}
    }
    
    print("Collecting statistics for early timeslots (1-5)...")
    for timeslot in early_slots:
        metrics = comm_manager.get_link_quality_metrics(timeslot)
        stats['early']['isl_links'].append(metrics['isl']['total_links'])
        stats['early']['sg_links'].append(metrics['satellite_ground']['total_links'])
        stats['early']['sc_links'].append(metrics['satellite_cloud']['total_links'])
        stats['early']['sg_uplink'].append(metrics['satellite_ground']['avg_uplink_rate_mbps'])
        stats['early']['sg_downlink'].append(metrics['satellite_ground']['avg_downlink_rate_mbps'])
        stats['early']['sc_uplink'].append(metrics['satellite_cloud']['avg_uplink_rate_mbps'])
        stats['early']['sc_downlink'].append(metrics['satellite_cloud']['avg_downlink_rate_mbps'])
    
    print("Collecting statistics for later timeslots (101-105)...")
    for timeslot in later_slots:
        metrics = comm_manager.get_link_quality_metrics(timeslot)
        stats['later']['isl_links'].append(metrics['isl']['total_links'])
        stats['later']['sg_links'].append(metrics['satellite_ground']['total_links'])
        stats['later']['sc_links'].append(metrics['satellite_cloud']['total_links'])
        stats['later']['sg_uplink'].append(metrics['satellite_ground']['avg_uplink_rate_mbps'])
        stats['later']['sg_downlink'].append(metrics['satellite_ground']['avg_downlink_rate_mbps'])
        stats['later']['sc_uplink'].append(metrics['satellite_cloud']['avg_uplink_rate_mbps'])
        stats['later']['sc_downlink'].append(metrics['satellite_cloud']['avg_downlink_rate_mbps'])
    
    print("\n[COMPARISON RESULTS]")
    print("-" * 50)
    
    print("\nVisible Links Count:")
    print(f"  ISL Links:")
    print(f"    Early (1-5): avg={np.mean(stats['early']['isl_links']):.1f}, std={np.std(stats['early']['isl_links']):.1f}")
    print(f"    Later (101-105): avg={np.mean(stats['later']['isl_links']):.1f}, std={np.std(stats['later']['isl_links']):.1f}")
    print(f"    Change: {np.mean(stats['later']['isl_links']) - np.mean(stats['early']['isl_links']):.1f}")
    
    print(f"\n  Satellite-Ground Links:")
    print(f"    Early (1-5): avg={np.mean(stats['early']['sg_links']):.1f}, std={np.std(stats['early']['sg_links']):.1f}")
    print(f"    Later (101-105): avg={np.mean(stats['later']['sg_links']):.1f}, std={np.std(stats['later']['sg_links']):.1f}")
    print(f"    Change: {np.mean(stats['later']['sg_links']) - np.mean(stats['early']['sg_links']):.1f}")
    
    print(f"\n  Satellite-Cloud Links:")
    print(f"    Early (1-5): avg={np.mean(stats['early']['sc_links']):.1f}, std={np.std(stats['early']['sc_links']):.1f}")
    print(f"    Later (101-105): avg={np.mean(stats['later']['sc_links']):.1f}, std={np.std(stats['later']['sc_links']):.1f}")
    print(f"    Change: {np.mean(stats['later']['sc_links']) - np.mean(stats['early']['sc_links']):.1f}")
    
    print("\nAverage Data Rates (Mbps):")
    print(f"  Satellite-Ground Uplink:")
    print(f"    Early (1-5): {np.mean(stats['early']['sg_uplink']):.2f} Mbps")
    print(f"    Later (101-105): {np.mean(stats['later']['sg_uplink']):.2f} Mbps")
    
    print(f"\n  Satellite-Ground Downlink:")
    print(f"    Early (1-5): {np.mean(stats['early']['sg_downlink']):.2f} Mbps")
    print(f"    Later (101-105): {np.mean(stats['later']['sg_downlink']):.2f} Mbps")
    
    print(f"\n  Satellite-Cloud Uplink:")
    print(f"    Early (1-5): {np.mean(stats['early']['sc_uplink']):.2f} Mbps")
    print(f"    Later (101-105): {np.mean(stats['later']['sc_uplink']):.2f} Mbps")
    
    print(f"\n  Satellite-Cloud Downlink:")
    print(f"    Early (1-5): {np.mean(stats['early']['sc_downlink']):.2f} Mbps")
    print(f"    Later (101-105): {np.mean(stats['later']['sc_downlink']):.2f} Mbps")


if __name__ == "__main__":
    # Run main test
    test_communication_matrices_by_timeslot()
    
    # Run comparison
    compare_communication_across_timeslots()