
"""
任务生成模拟器
根据区域任务生成模型，为420个地理坐标点生成任务数据
"""

import csv
import json
import random
import numpy as np
import yaml
from typing import List, Dict, Any
from datetime import datetime
import os
from pathlib import Path

# 使用绝对导入加载配置
from src.env.Foundation_Layer.logging_config import get_logger

class Location:
    """地理位置节点类"""
    def __init__(self, location_id: int, latitude: float, longitude: float, 
                 geography: str, scale: str, functional_type: str):
        self.location_id = location_id
        self.latitude = latitude
        self.longitude = longitude
        self.geography = geography  # Land/Ocean
        self.scale = scale  # Small/Medium/Large
        self.functional_type = functional_type  # Normal/Industrial/DelaySensitive
        self.coordinates = (latitude, longitude)

class Task:
    """任务类"""
    def __init__(self, task_id: int, type_id: int, data_size_mb: float, 
                 complexity_cycles_per_bit: int, deadline_timestamp: float, priority: int):
        self.task_id = task_id
        self.type_id = type_id
        self.data_size_mb = data_size_mb
        self.complexity_cycles_per_bit = complexity_cycles_per_bit
        self.deadline_timestamp = deadline_timestamp
        self.priority = priority
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "task_id": self.task_id,
            "type_id": self.type_id,
            "data_size_mb": self.data_size_mb,
            "complexity_cycles_per_bit": self.complexity_cycles_per_bit,
            "deadline_timestamp": self.deadline_timestamp,
            "priority": self.priority
        }

class TaskGenerator:
    """任务生成器"""
    
    def __init__(self):
        self.task_id_counter = 0
        self.current_time = 0
        self.locations: List[Location] = []
        
    def load_locations_from_csv(self, csv_file_path: str) -> None:
        """从CSV文件加载地理位置数据"""
        self.locations = []
        with open(csv_file_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                location = Location(
                    location_id=int(row['ID']),
                    latitude=float(row['Latitude']),
                    longitude=float(row['Longitude']),
                    geography=row['RegionType'],  # Land/Ocean
                    scale=row['Size'],           # Small/Medium/Large
                    functional_type=row['PurposeType']  # Normal/Industrial/DelaySensitive
                )
                self.locations.append(location)
        
        print(f"成功加载 {len(self.locations)} 个地理位置")
    
    def calculate_lambda(self, location: Location) -> float:
        """计算任务生成率 λ_i"""
        if location.geography == 'Ocean':
            return 1.0
        elif location.geography == 'Land':
            if location.scale == 'Large':
                return 10.0
            elif location.scale == 'Medium':
                return 6.0
            elif location.scale == 'Small':
                return 3.0
        return 1.0  # 默认值
    
    def sample_task_type(self, functional_type: str) -> int:
        """根据功能类型采样任务类型"""
        # 定义概率分布
        probabilities = {
            'Normal': [0.2, 0.6, 0.2],      # type 1
            'Industrial': [0.2, 0.2, 0.6],  # type 2
            'DelaySensitive': [0.6, 0.2, 0.2]  # type 3
        }
        
        prob = probabilities.get(functional_type, [0.33, 0.33, 0.34])
        return np.random.choice([1, 2, 3], p=prob)
    
    def generate_task_priority(self) -> int:
        """生成任务优先级 (1-5)"""
        # 使用均匀分布随机生成1-5的优先级
        return np.random.randint(1, 4)
    
    def generate_task_parameters(self, task_type: int, current_time: float) -> Dict[str, Any]:
        """根据任务类型生成任务参数"""
        if task_type == 1:
            data_size_mb = np.random.uniform(10, 20)
            complexity = 100
            deadline = current_time + 5
        elif task_type == 2:
            data_size_mb = np.random.uniform(20, 50)
            complexity = 200
            deadline = current_time + 15
        elif task_type == 3:
            data_size_mb = np.random.uniform(50, 150)
            complexity = 300
            deadline = current_time + 30
        else:
            # 默认参数
            data_size_mb = np.random.uniform(10, 50)
            complexity = 150
            deadline = current_time + 30
        
        # 生成优先级
        priority = self.generate_task_priority()
        
        return {
            'data_size_mb': data_size_mb,
            'complexity_cycles_per_bit': complexity,
            'deadline_timestamp': deadline,
            'priority': priority
        }
    
    def generate_tasks_for_location(self, location: Location, current_time: float) -> List[Task]:
        """为单个位置生成任务"""
        # 1. 计算任务生成率
        lambda_i = self.calculate_lambda(location)
        
        # 2. 使用泊松分布确定任务数量
        num_tasks = np.random.poisson(lambda_i)
        
        # 3. 生成每个任务
        tasks = []
        for _ in range(num_tasks):
            # 3a. 任务类型采样
            task_type = self.sample_task_type(location.functional_type)
            
            # 3b. 任务参数生成
            params = self.generate_task_parameters(task_type, current_time)
            
            # 3c. 创建任务对象
            self.task_id_counter += 1
            task = Task(
                task_id=self.task_id_counter,
                type_id=task_type,
                data_size_mb=params['data_size_mb'],
                complexity_cycles_per_bit=params['complexity_cycles_per_bit'],
                deadline_timestamp=params['deadline_timestamp'],
                priority=params['priority']
            )
            tasks.append(task)
        
        return tasks
    
    def run_simulation(self) -> Dict[str, Any]:
        """运行完整的模拟"""
        print("开始任务生成模拟...")
        
        simulation_results = []
        
        # 主循环：遍历时隙和地点
        for timeslot_index in range(TOTAL_TIMESLOTS):
            self.current_time = timeslot_index * TIMESLOT_DURATION
            print(f"处理时隙 {timeslot_index + 1}/{TOTAL_TIMESLOTS}, 时间: {self.current_time}s")
            
            timeslot_data = {
                "timeslot": timeslot_index,
                "timestamp": self.current_time,
                "locations": []
            }
            
            total_tasks_in_timeslot = 0
            
            # 遍历所有地理位置
            for location in self.locations:
                # 为当前位置生成任务
                generated_tasks = self.generate_tasks_for_location(location, self.current_time)
                total_tasks_in_timeslot += len(generated_tasks)
                
                # 保存位置数据
                location_data = {
                    "location_id": location.location_id,
                    "coordinates": [location.latitude, location.longitude],
                    "geography": location.geography,
                    "scale": location.scale,
                    "functional_type": location.functional_type,
                    "lambda_i": self.calculate_lambda(location),
                    "num_tasks": len(generated_tasks),
                    "generated_tasks": [task.to_dict() for task in generated_tasks]
                }
                timeslot_data["locations"].append(location_data)
            
            timeslot_data["total_tasks"] = total_tasks_in_timeslot
            simulation_results.append(timeslot_data)
            print(f"  - 时隙 {timeslot_index + 1} 总计生成 {total_tasks_in_timeslot} 个任务")
        
        # 生成最终结果
        final_result = {
            "simulation_metadata": {
                "total_locations": TOTAL_LOCATIONS,
                "total_timeslots": TOTAL_TIMESLOTS,
                "timeslot_duration_seconds": TIMESLOT_DURATION,
                "land_base_rate": LAND_BASE_RATE,
                "total_tasks_generated": self.task_id_counter,
                "generation_timestamp": datetime.now().isoformat()
            },
            "simulation_results": simulation_results
        }
        
        print(f"模拟完成！总计生成 {self.task_id_counter} 个任务")
        return final_result
    
    def save_results_to_file(self, results: Dict[str, Any], output_file: str) -> None:
        """保存结果到JSON文件"""
        # 自定义JSON编码器来处理numpy数据类型
        class NumpyEncoder(json.JSONEncoder):
            def default(self, obj):
                if isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                return super(NumpyEncoder, self).default(obj)
        
        with open(output_file, 'w', encoding='utf-8') as file:
            json.dump(results, file, ensure_ascii=False, indent=2, cls=NumpyEncoder)
        print(f"结果已保存到: {output_file}")

def main():
    """主函数"""
    # 设置随机种子以保证结果可重现
    np.random.seed(42)
    random.seed(42)
    
    # 创建任务生成器
    generator = TaskGenerator()
    
    # 获取当前脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    csv_path = os.path.join(script_dir,  "updated_global_ground_stations.csv")
    generator.load_locations_from_csv(csv_path)
    
    # 运行模拟
    results = generator.run_simulation()
    
    # 保存结果
    output_file = "task_generation_results.json"
    generator.save_results_to_file(results, output_file)
    
    # 打印统计信息
    print("\n=== 模拟统计信息 ===")
    print(f"总地理位置数: {len(generator.locations)}")
    print(f"总时隙数: {TOTAL_TIMESLOTS}")
    print(f"每时隙持续时间: {TIMESLOT_DURATION}秒")
    print(f"总生成任务数: {generator.task_id_counter}")
    
    # 按地理类型统计
    land_count = sum(1 for loc in generator.locations if loc.geography == 'Land')
    ocean_count = sum(1 for loc in generator.locations if loc.geography == 'Ocean')
    print(f"陆地位置: {land_count}, 海洋位置: {ocean_count}")
    
    # 按规模统计
    scale_counts = {}
    for loc in generator.locations:
        scale_counts[loc.scale] = scale_counts.get(loc.scale, 0) + 1
    print(f"规模分布: {scale_counts}")
    
    # 按功能类型统计
    func_counts = {}
    for loc in generator.locations:
        func_counts[loc.functional_type] = func_counts.get(loc.functional_type, 0) + 1
    print(f"功能类型分布: {func_counts}")

if __name__ == "__main__":
    main() 
