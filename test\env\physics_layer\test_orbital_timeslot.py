"""
Orbital Module Test - Timeslot-based Visibility Matrix Output
测试orbital.py模块的可见性矩阵计算功能
按照编程规范要求，输出特定时隙的完整可见性矩阵并进行统计分析
"""
import sys
import os
from pathlib import Path
import numpy as np
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.env.physics_layer.orbital import OrbitalUpdater


def test_visibility_matrices_by_timeslot():
    """
    测试不同时隙的可见性矩阵
    输出时隙1-5, 101-105的所有三种可见性矩阵
    """
    print("=" * 80)
    print("Orbital Module Visibility Matrix Test")
    print("Testing timeslots: 1-5, 101-105")
    print("=" * 80)
    
    # Initialize OrbitalUpdater
    current_dir = Path(__file__).parent.parent.parent.parent
    config_file = current_dir / "src" / "env" / "physics_layer" / "config.yaml"
    data_file = current_dir / "src" / "env" / "env_data" / "satellite_data72_1.csv"
    
    try:
        orbital = OrbitalUpdater(
            data_file=str(data_file),
            config_file=str(config_file)
        )
        print(f"[INIT] OrbitalUpdater initialized successfully")
        print(f"[INFO] Total timeslots: {orbital.get_total_timeslots()}")
        print(f"[INFO] Number of satellites: {orbital.config['system']['num_leo_satellites']}")
        print(f"[INFO] Number of ground stations: {orbital.get_ground_station_count()}")
        print(f"[INFO] Number of cloud centers: {orbital.get_cloud_station_count()}")
        print()
        
    except Exception as e:
        print(f"[ERROR] Failed to initialize OrbitalUpdater: {e}")
        return
    
    # Test timeslots
    test_timeslots = list(range(1, 6)) + list(range(101, 106))
    
    for timeslot in test_timeslots:
        print("=" * 80)
        print(f"TIMESLOT {timeslot}")
        print("=" * 80)
        
        try:
            # Get satellites at this timeslot
            satellites = orbital.get_satellites_at_time(timeslot)
            
            if not satellites:
                print(f"[WARNING] No satellites found at timeslot {timeslot}")
                continue
                
            # Get time context
            time_context = orbital.time_manager.get_time_context(timeslot)
            print(f"[TIME] Physical time: {time_context.physical_time}")
            print(f"[TIME] Simulation time: {time_context.simulation_time:.2f}s")
            print()
            
            # 1. Inter-satellite visibility matrix
            print("-" * 40)
            print("1. INTER-SATELLITE VISIBILITY MATRIX")
            print("-" * 40)
            vis_matrix, dist_matrix = orbital.build_visibility_matrix(satellites)
            
            print(f"Matrix shape: {vis_matrix.shape}")
            print(f"Total possible connections: {vis_matrix.size - len(satellites)}")  # Exclude diagonal
            print(f"Visible connections: {np.sum(vis_matrix)}")
            print(f"Visibility percentage: {np.sum(vis_matrix) / (vis_matrix.size - len(satellites)) * 100:.2f}%")
            
            # Statistics
            if vis_matrix.size > 0:
                # Count connections per satellite
                connections_per_sat = np.sum(vis_matrix, axis=1)
                print(f"Average connections per satellite: {np.mean(connections_per_sat):.2f}")
                print(f"Max connections: {np.max(connections_per_sat)}")
                print(f"Min connections: {np.min(connections_per_sat)}")
                
                # Distance statistics for visible connections
                visible_distances = dist_matrix[vis_matrix]
                if len(visible_distances) > 0:
                    print(f"Average distance for visible connections: {np.mean(visible_distances):.2f} km")
                    print(f"Max distance: {np.max(visible_distances):.2f} km")
                    print(f"Min distance: {np.min(visible_distances):.2f} km")
            
            # Show first 5x5 of the matrix
            print("\nFirst 5x5 of visibility matrix:")
            print(vis_matrix[:5, :5].astype(int))
            print()
            
            # 2. Satellite-Ground visibility matrix
            print("-" * 40)
            print("2. SATELLITE-GROUND VISIBILITY MATRIX")
            print("-" * 40)
            vis_matrix_sg, dist_matrix_sg = orbital.build_satellite_ground_visibility_matrix(satellites, timeslot)
            
            print(f"Matrix shape: {vis_matrix_sg.shape} (satellites x ground_stations)")
            print(f"Total possible connections: {vis_matrix_sg.size}")
            print(f"Visible connections: {np.sum(vis_matrix_sg)}")
            print(f"Visibility percentage: {np.sum(vis_matrix_sg) / vis_matrix_sg.size * 100:.2f}%")
            
            # Statistics
            if vis_matrix_sg.size > 0:
                # Ground stations visible per satellite
                ground_per_sat = np.sum(vis_matrix_sg, axis=1)
                print(f"Average ground stations visible per satellite: {np.mean(ground_per_sat):.2f}")
                print(f"Max ground stations visible: {np.max(ground_per_sat)}")
                print(f"Min ground stations visible: {np.min(ground_per_sat)}")
                
                # Satellites visible per ground station
                sat_per_ground = np.sum(vis_matrix_sg, axis=0)
                print(f"Average satellites visible per ground station: {np.mean(sat_per_ground):.2f}")
                print(f"Max satellites visible: {np.max(sat_per_ground)}")
                print(f"Min satellites visible: {np.min(sat_per_ground)}")
                
                # Distance statistics
                visible_distances_sg = dist_matrix_sg[vis_matrix_sg]
                if len(visible_distances_sg) > 0:
                    print(f"Average distance for visible connections: {np.mean(visible_distances_sg):.2f} km")
                    print(f"Max distance: {np.max(visible_distances_sg):.2f} km")
                    print(f"Min distance: {np.min(visible_distances_sg):.2f} km")
            
            # Show first 5x5 of the matrix
            print("\nFirst 5x5 of visibility matrix:")
            print(vis_matrix_sg[:5, :5].astype(int))
            print()
            
            # 3. Satellite-Cloud visibility matrix
            print("-" * 40)
            print("3. SATELLITE-CLOUD VISIBILITY MATRIX")
            print("-" * 40)
            vis_matrix_sc, dist_matrix_sc = orbital.build_satellite_cloud_visibility_matrix(satellites, timeslot)
            
            print(f"Matrix shape: {vis_matrix_sc.shape} (satellites x cloud_centers)")
            print(f"Total possible connections: {vis_matrix_sc.size}")
            print(f"Visible connections: {np.sum(vis_matrix_sc)}")
            print(f"Visibility percentage: {np.sum(vis_matrix_sc) / vis_matrix_sc.size * 100:.2f}%")
            
            # Statistics
            if vis_matrix_sc.size > 0:
                # Cloud centers visible per satellite
                cloud_per_sat = np.sum(vis_matrix_sc, axis=1)
                print(f"Average cloud centers visible per satellite: {np.mean(cloud_per_sat):.2f}")
                print(f"Max cloud centers visible: {np.max(cloud_per_sat)}")
                print(f"Min cloud centers visible: {np.min(cloud_per_sat)}")
                
                # Satellites visible per cloud center
                sat_per_cloud = np.sum(vis_matrix_sc, axis=0)
                print(f"Average satellites visible per cloud center: {np.mean(sat_per_cloud):.2f}")
                print(f"Max satellites visible: {np.max(sat_per_cloud)}")
                print(f"Min satellites visible: {np.min(sat_per_cloud)}")
                
                # Distance statistics
                visible_distances_sc = dist_matrix_sc[vis_matrix_sc]
                if len(visible_distances_sc) > 0:
                    print(f"Average distance for visible connections: {np.mean(visible_distances_sc):.2f} km")
                    print(f"Max distance: {np.max(visible_distances_sc):.2f} km")
                    print(f"Min distance: {np.min(visible_distances_sc):.2f} km")
            
            # Show full matrix (since it's small)
            print("\nFull visibility matrix (satellites x cloud centers):")
            print(vis_matrix_sc.astype(int))
            print()
            
        except Exception as e:
            print(f"[ERROR] Failed to process timeslot {timeslot}: {e}")
            import traceback
            traceback.print_exc()
    
    print("=" * 80)
    print("TEST COMPLETED")
    print("=" * 80)


def compare_timeslots():
    """
    比较不同时隙之间的可见性变化
    """
    print("\n" + "=" * 80)
    print("VISIBILITY CHANGES COMPARISON")
    print("=" * 80)
    
    # Initialize OrbitalUpdater
    current_dir = Path(__file__).parent.parent.parent.parent
    config_file = current_dir / "src" / "env" / "physics_layer" / "config.yaml"
    data_file = current_dir / "src" / "env" / "env_data" / "satellite_data72_1.csv"
    
    try:
        orbital = OrbitalUpdater(
            data_file=str(data_file),
            config_file=str(config_file)
        )
    except Exception as e:
        print(f"[ERROR] Failed to initialize: {e}")
        return
    
    # Compare early timeslots (1-5) with later timeslots (101-105)
    early_slots = list(range(1, 6))
    later_slots = list(range(101, 106))
    
    early_stats = {'sat_sat': [], 'sat_ground': [], 'sat_cloud': []}
    later_stats = {'sat_sat': [], 'sat_ground': [], 'sat_cloud': []}
    
    print("Collecting statistics...")
    
    for timeslot in early_slots:
        satellites = orbital.get_satellites_at_time(timeslot)
        if satellites:
            vis_ss, _ = orbital.build_visibility_matrix(satellites)
            vis_sg, _ = orbital.build_satellite_ground_visibility_matrix(satellites, timeslot)
            vis_sc, _ = orbital.build_satellite_cloud_visibility_matrix(satellites, timeslot)
            
            early_stats['sat_sat'].append(np.sum(vis_ss))
            early_stats['sat_ground'].append(np.sum(vis_sg))
            early_stats['sat_cloud'].append(np.sum(vis_sc))
    
    for timeslot in later_slots:
        satellites = orbital.get_satellites_at_time(timeslot)
        if satellites:
            vis_ss, _ = orbital.build_visibility_matrix(satellites)
            vis_sg, _ = orbital.build_satellite_ground_visibility_matrix(satellites, timeslot)
            vis_sc, _ = orbital.build_satellite_cloud_visibility_matrix(satellites, timeslot)
            
            later_stats['sat_sat'].append(np.sum(vis_ss))
            later_stats['sat_ground'].append(np.sum(vis_sg))
            later_stats['sat_cloud'].append(np.sum(vis_sc))
    
    print("\nComparison Results:")
    print("-" * 40)
    
    print("\nInter-Satellite Visibility:")
    print(f"  Early slots (1-5) average: {np.mean(early_stats['sat_sat']):.2f} connections")
    print(f"  Later slots (101-105) average: {np.mean(later_stats['sat_sat']):.2f} connections")
    print(f"  Change: {(np.mean(later_stats['sat_sat']) - np.mean(early_stats['sat_sat'])):.2f}")
    
    print("\nSatellite-Ground Visibility:")
    print(f"  Early slots (1-5) average: {np.mean(early_stats['sat_ground']):.2f} connections")
    print(f"  Later slots (101-105) average: {np.mean(later_stats['sat_ground']):.2f} connections")
    print(f"  Change: {(np.mean(later_stats['sat_ground']) - np.mean(early_stats['sat_ground'])):.2f}")
    
    print("\nSatellite-Cloud Visibility:")
    print(f"  Early slots (1-5) average: {np.mean(early_stats['sat_cloud']):.2f} connections")
    print(f"  Later slots (101-105) average: {np.mean(later_stats['sat_cloud']):.2f} connections")
    print(f"  Change: {(np.mean(later_stats['sat_cloud']) - np.mean(early_stats['sat_cloud'])):.2f}")


if __name__ == "__main__":
    # Run main test
    test_visibility_matrices_by_timeslot()
    
    # Run comparison
    compare_timeslots()