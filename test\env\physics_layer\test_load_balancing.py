"""
负载均衡模块测试文件

测试区域负载均衡计算功能，包括：
- 卫星区域分组
- 负载方差计算
- 结果验证

Author: SPACE2 Team
Version: 1.0
"""

import pytest
import yaml
import numpy as np
import sys
import os
from datetime import datetime
from typing import Dict, List

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from src.env.physics_layer.load_balancing import LoadBalancingCalculator, RegionalLoadResult, SatelliteLoadInfo
from src.env.Foundation_Layer.time_manager import TimeContext, TimeManager


class TestLoadBalancingCalculator:
    """负载均衡计算器测试类"""
    
    @classmethod
    def setup_class(cls):
        """测试类初始化"""
        # 加载配置
        with open("src/env/physics_layer/config.yaml", 'r', encoding='utf-8') as f:
            cls.config = yaml.safe_load(f)
            
        # 创建负载均衡计算器
        cls.calculator = LoadBalancingCalculator(cls.config)
        
        # 创建时间管理器
        start_time = datetime(2025, 6, 8, 4, 0, 0)  # 仿真开始时间
        timeslot_duration = cls.config['system']['timeslot_duration_s']
        total_timeslots = cls.config['system']['total_timeslots']
        cls.time_manager = TimeManager(start_time, timeslot_duration, total_timeslots)
    
    def create_test_satellite_data(self, count: int = 10) -> List[Dict]:
        """创建测试用的卫星数据"""
        satellites = []
        
        # 在不同区域创建测试卫星
        test_positions = [
            (45.0, -150.0),   # 区域1: 北美高纬度
            (45.0, -90.0),    # 区域2: 北美高纬度
            (45.0, -30.0),    # 区域3: 欧洲高纬度
            (45.0, 30.0),     # 区域4: 亚洲高纬度
            (20.0, -150.0),   # 区域7: 北美中纬度
            (20.0, -90.0),    # 区域8: 北美中纬度
            (20.0, -30.0),    # 区域9: 欧洲中纬度
            (20.0, 30.0),     # 区域10: 亚洲中纬度
            (-20.0, -150.0),  # 区域13: 南美中低纬度
            (-20.0, 30.0),    # 区域16: 非洲中低纬度
        ]
        
        for i in range(min(count, len(test_positions))):
            lat, lon = test_positions[i]
            satellites.append({
                'satellite_id': i + 1,
                'latitude': lat,
                'longitude': lon,
                'cpu_utilization': np.random.uniform(0.1, 0.9),  # 随机CPU利用率
                'queue_length': np.random.randint(0, 50)         # 随机队列长度
            })
        
        return satellites
    
    def test_region_assignment(self):
        """测试卫星区域分配功能"""
        print("\n=== 测试卫星区域分配 ===")
        
        # 测试已知位置的区域分配
        test_cases = [
            (45.0, -150.0, 1),   # 应该分配到区域1
            (45.0, -90.0, 2),    # 应该分配到区域2  
            (20.0, -30.0, 9),    # 应该分配到区域9
            (-20.0, 30.0, 16),   # 应该分配到区域16
        ]
        
        for lat, lon, expected_region in test_cases:
            region_id = self.calculator._determine_satellite_region(lat, lon)
            print(f"位置({lat}, {lon}) -> 区域{region_id} (期望: {expected_region})")
            assert region_id == expected_region, f"区域分配错误: 期望{expected_region}, 实际{region_id}"
    
    def test_load_variance_calculation(self):
        """测试负载方差计算"""
        print("\n=== 测试负载方差计算 ===")
        
        # 创建测试卫星负载信息
        satellites_in_region = [
            SatelliteLoadInfo(1, 1, 0.5, 10, 0.53, 45.0, -150.0),  # load = 0.7*0.5 + 0.3*0.1 = 0.38
            SatelliteLoadInfo(2, 1, 0.8, 20, 0.62, 45.0, -149.0),  # load = 0.7*0.8 + 0.3*0.2 = 0.62  
            SatelliteLoadInfo(3, 1, 0.3, 5, 0.26, 45.0, -148.0),   # load = 0.7*0.3 + 0.3*0.05 = 0.225
        ]
        
        # 重新计算准确的负载评分
        for sat in satellites_in_region:
            sat.load_score = (self.calculator.w_cpu * sat.cpu_utilization + 
                             self.calculator.w_queue * sat.queue_length / self.calculator.max_queue_size)
        
        variance, mean_load = self.calculator._calculate_regional_load_variance(satellites_in_region)
        
        print(f"区域内卫星负载: {[sat.load_score for sat in satellites_in_region]}")
        print(f"平均负载: {mean_load:.4f}")
        print(f"负载方差: {variance:.6f}")
        
        # 验证计算结果
        expected_loads = [sat.load_score for sat in satellites_in_region]
        expected_mean = np.mean(expected_loads)
        expected_variance = np.var(expected_loads, ddof=0)
        
        assert abs(mean_load - expected_mean) < 1e-6, "平均负载计算错误"
        assert abs(variance - expected_variance) < 1e-6, "方差计算错误"
    
    def test_single_timeslot_calculation(self):
        """测试单时隙负载均衡计算"""
        print("\n=== 测试单时隙负载均衡计算 ===")
        
        timeslot = 100
        satellites_data = self.create_test_satellite_data(10)
        
        print(f"测试时隙: {timeslot}")
        print(f"测试卫星数量: {len(satellites_data)}")
        
        # 执行负载均衡计算
        result = self.calculator.calculate_load_balance(timeslot, satellites_data)
        
        # 验证结果
        assert result.timeslot == timeslot
        assert result.total_regions_active > 0
        assert len(result.regional_load_variances) == result.total_regions_active
        
        print(f"活跃区域数量: {result.total_regions_active}")
        print(f"区域负载方差: {result.regional_load_variances}")
        print(f"区域平均负载: {result.regional_mean_loads}")
        print(f"区域卫星数量: {result.regional_satellite_counts}")
        
        # 验证各区域数据一致性
        for region_id in result.regional_load_variances.keys():
            assert region_id in result.regional_mean_loads
            assert region_id in result.regional_satellite_counts
            assert result.regional_satellite_counts[region_id] > 0
    
    def test_load_balance_summary(self):
        """测试负载均衡结果摘要"""
        print("\n=== 测试负载均衡结果摘要 ===")
        
        # 创建测试数据并计算
        satellites_data = self.create_test_satellite_data(15)
        result = self.calculator.calculate_load_balance(200, satellites_data)
        
        # 获取摘要
        summary = self.calculator.get_load_balance_summary(result)
        
        print(f"负载均衡摘要:")
        for key, value in summary.items():
            print(f"  {key}: {value}")
        
        # 验证摘要数据
        assert summary['timeslot'] == 200
        assert summary['total_regions'] == result.total_regions_active
        assert summary['total_satellites'] == sum(result.regional_satellite_counts.values())
        
        if result.total_regions_active > 0:
            assert summary['average_variance'] >= 0
            assert summary['max_variance'] >= summary['min_variance']
    
    def test_multiple_timeslots(self):
        """测试多时隙连续计算"""
        print("\n=== 测试多时隙连续计算 ===")
        
        test_timeslots = [1, 100, 500, 1000, 1440]
        
        for timeslot in test_timeslots:
            print(f"\n--- 时隙 {timeslot} ---")
            
            # 获取时间上下文
            context = self.time_manager.get_time_context(timeslot)
            print(f"仿真时间: {context.simulation_time}s")
            print(f"物理时间: {context.physical_time}")
            
            # 创建该时隙的测试数据
            satellites_data = self.create_test_satellite_data(12)
            
            # 计算负载均衡
            result = self.calculator.calculate_load_balance(timeslot, satellites_data)
            summary = self.calculator.get_load_balance_summary(result)
            
            print(f"活跃区域: {summary['total_regions']}")
            print(f"平均方差: {summary['average_variance']:.6f}")
            print(f"最大方差: {summary['max_variance']:.6f}")
            print(f"最小方差: {summary['min_variance']:.6f}")
            
            # 验证计算结果
            assert result.timeslot == timeslot
            assert isinstance(summary['average_variance'], (int, float))
    
    def test_edge_cases(self):
        """测试边界情况"""
        print("\n=== 测试边界情况 ===")
        
        # 测试空卫星列表
        empty_result = self.calculator.calculate_load_balance(1, [])
        assert empty_result.total_regions_active == 0
        assert len(empty_result.regional_load_variances) == 0
        print("空卫星列表测试通过")
        
        # 测试单颗卫星
        single_satellite = [{
            'satellite_id': 1,
            'latitude': 45.0,
            'longitude': -150.0,
            'cpu_utilization': 0.5,
            'queue_length': 10
        }]
        
        single_result = self.calculator.calculate_load_balance(1, single_satellite)
        assert single_result.total_regions_active == 1
        
        # 单颗卫星的方差应该为0
        variance = list(single_result.regional_load_variances.values())[0]
        assert variance == 0.0
        print("单颗卫星测试通过")
        
        # 测试边界位置卫星 (边界值)
        boundary_satellites = [
            {'satellite_id': 1, 'latitude': 90.0, 'longitude': -180.0, 'cpu_utilization': 0.5, 'queue_length': 10},
            {'satellite_id': 2, 'latitude': -90.0, 'longitude': 180.0, 'cpu_utilization': 0.3, 'queue_length': 5},
        ]
        
        boundary_result = self.calculator.calculate_load_balance(1, boundary_satellites)
        print(f"边界位置测试: {boundary_result.total_regions_active}个活跃区域")
        print("边界位置测试通过")


def run_load_balancing_tests():
    """运行所有负载均衡测试"""
    print("=" * 60)
    print("负载均衡模块测试")
    print("=" * 60)
    
    # 创建测试实例
    test_instance = TestLoadBalancingCalculator()
    test_instance.setup_class()
    
    # 运行所有测试
    try:
        test_instance.test_region_assignment()
        test_instance.test_load_variance_calculation()
        test_instance.test_single_timeslot_calculation()
        test_instance.test_load_balance_summary()
        test_instance.test_multiple_timeslots()
        test_instance.test_edge_cases()
        
        print("\n" + "=" * 60)
        print("所有负载均衡测试通过!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n测试失败: {str(e)}")
        raise


if __name__ == "__main__":
    run_load_balancing_tests()