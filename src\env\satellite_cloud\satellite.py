"""
卫星任务处理模块
实现基于动态优先级评分的排队调度模型（DPSQ）
"""

import numpy as np
from typing import List, Dict, Optional, Tuple, Any
import heapq
from dataclasses import dataclass, field
import heapq
from enum import Enum
import logging
import yaml
from pathlib import Path

# 使用绝对导入
from src.env.physics_layer.task_tracking import TaskTrackingRecord, TaskStatus as TrackingStatus, ProcessingNode, NodeType
from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.physics_layer.communication_refactored import CommunicationManager

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"          # 等待中
    PROCESSING = "processing"    # 处理中
    COMPLETED = "completed"      # 已完成
    DROPPED = "dropped"          # 已丢弃
    FAILED = "failed"           # 失败

@dataclass
class SatelliteTask:
    """卫星任务数据类"""
    task_id: str
    priority: float              # 静态优先级 P_i
    deadline: float             # 绝对截止时间 D_i (秒)
    data_size: float            # 数据大小 S_i (MB)
    complexity: float           # 计算复杂度 C_i (CPU cycles)
    drop_penalty: float         # 丢弃惩罚 W_i
    arrival_time: float         # 到达时间
    start_time: Optional[float] = None      # 开始处理时间
    completion_time: Optional[float] = None  # 完成时间
    status: TaskStatus = TaskStatus.PENDING  # 任务状态
    allocated_cpu: float = 0.0              # 分配的CPU资源比例
    energy_consumed: float = 0.0            # 消耗的能量
    processing_delay: float = 0.0           # 处理延迟
    communication_delay: float = 0.0        # 通信延迟
    total_delay: float = 0.0                # 总延迟
    
    # 跨时隙处理支持字段
    processing_progress: float = 0.0         # 处理进度(0-1)
    remaining_complexity: float = 0.0        # 剩余计算复杂度
    accumulated_processing_time: float = 0.0 # 累计处理时间
    accumulated_energy: float = 0.0          # 累计能量消耗
    last_update_time: Optional[float] = None # 上次更新时间
    is_partial_processing: bool = False      # 是否部分处理
    segment_ratio: float = 1.0               # 任务分片比例
    
    def __lt__(self, other):
        """用于优先队列比较"""
        return self.task_id < other.task_id

class DPSQScheduler:
    """动态优先级评分调度器"""
    
    def __init__(self, config: Dict):
        """
        初始化调度器
        
        Args:
            config: 配置字典
        """
        # 从配置中提取参数
        self.w_priority = config['queuing']['w_priority']
        self.w_urgency = config['queuing']['w_urgency']
        self.w_cost = config['queuing']['w_cost']
        self.epsilon_urgency = config['queuing']['epsilon_urgency']
        self.max_queue_size = config['queuing']['max_queue_size']
        
        # 计算参数
        self.f_sat = float(config['computation']['f_leo_hz'])  # 卫星CPU频率
        self.default_bandwidth = config['communication']['b_us_hz'] / 8e6  # 转换为MB/s
        
        # 能效参数
        self.zeta_leo = float(config['computation']['zeta_leo'])
        
        # 处理开销
        self.processing_overhead = config['computation']['processing_overhead_ratio']
        
        self.logger = logging.getLogger(__name__)
        
    def calculate_priority_score(self, task: SatelliteTask, current_time: float, 
                                bandwidth: Optional[float] = None) -> float:
        """
        计算任务的动态优先级分数
        
        Score(T_i, t_now) = w_p * f_p(P_i) + w_d * f_d(D_i, t_now) - w_c * f_c(S_i, C_i)
        
        Args:
            task: 任务对象
            current_time: 当前时间
            bandwidth: 可用带宽（MB/s），如果为None则使用默认值
            
        Returns:
            动态优先级分数
        """
        if bandwidth is None or bandwidth <= 0:
            bandwidth = self.default_bandwidth
            
        # 优先级因子 f_p(P_i) = P_i
        f_priority = task.priority
        
        # 紧迫性因子 f_d(D_i, t_now) = 1 / ((D_i - t_now) + epsilon)
        time_remaining = max(task.deadline - current_time, 0)
        f_urgency = 1.0 / (time_remaining + self.epsilon_urgency)
        
        # 成本因子 f_c(S_i, C_i) = T_proc,i = S_i/B_link + C_i/F_sat
        # 防止除零错误
        if bandwidth > 0:
            communication_time = task.data_size / bandwidth
        else:
            communication_time = float('inf')  # 无法通信时设为无穷大
        computation_time = task.complexity / self.f_sat
        f_cost = communication_time + computation_time
        
        # 计算总分
        score = (self.w_priority * f_priority + 
                self.w_urgency * f_urgency - 
                self.w_cost * f_cost)
        
        return score
    
    def estimate_processing_time(self, task: SatelliteTask, 
                                cpu_allocation: float = 1.0,
                                bandwidth: Optional[float] = None) -> Tuple[float, float, float]:
        """
        估算任务处理时间
        
        Args:
            task: 任务对象
            cpu_allocation: CPU分配比例 (0-1)
            bandwidth: 可用带宽（MB/s）
            
        Returns:
            (总处理时间, 通信时间, 计算时间)
        """
        if bandwidth is None or bandwidth <= 0:
            bandwidth = self.default_bandwidth
            
        # 通信时延（防止除零）
        if bandwidth > 0:
            communication_time = task.data_size / bandwidth
        else:
            communication_time = float('inf')  # 无带宽时无法传输
        
        # 计算时延（考虑CPU分配比例）
        effective_cpu = self.f_sat * cpu_allocation
        computation_time = task.complexity / effective_cpu
        
        # 考虑处理开销
        total_time = (communication_time + computation_time) * (1 + self.processing_overhead)
        
        return total_time, communication_time, computation_time
    
    def check_feasibility(self, task: SatelliteTask, current_time: float,
                         cpu_allocation: float = 1.0,
                         bandwidth: Optional[float] = None) -> bool:
        """
        检查任务是否可以在截止时间前完成
        
        Args:
            task: 任务对象
            current_time: 当前时间
            cpu_allocation: CPU分配比例
            bandwidth: 可用带宽
            
        Returns:
            True if feasible, False otherwise
        """
        total_time, _, _ = self.estimate_processing_time(task, cpu_allocation, bandwidth)
        estimated_completion = current_time + total_time
        
        return estimated_completion <= task.deadline

class Satellite:
    """卫星任务处理类"""
    
    def __init__(self, satellite_id: int, config_path: str, 
                 orbital_updater: Optional[OrbitalUpdater] = None,
                 comm_manager: Optional[CommunicationManager] = None):
        """
        初始化卫星
        
        Args:
            satellite_id: 卫星ID
            config_path: 配置文件路径（必须提供）
            orbital_updater: 轨道更新器实例
            comm_manager: 通信管理器实例
        """
        self.satellite_id = satellite_id
        
        # 加载配置（不使用默认值）
        if not config_path:
            raise ValueError("config_path must be provided")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 初始化轨道和通信管理器
        self.orbital_updater = orbital_updater
        self.comm_manager = comm_manager
        
        # 初始化调度器
        self.scheduler = DPSQScheduler(self.config)
        
        # 优化：使用优先级队列
        self.task_queue: List[SatelliteTask] = []
        self.task_priority_cache: Dict[str, float] = {}  # 缓存任务优先级分数
        
        # 当前处理的任务
        self.current_tasks: List[SatelliteTask] = []
        
        # 已完成任务
        self.completed_tasks: List[SatelliteTask] = []
        
        # 已丢弃任务
        self.dropped_tasks: List[SatelliteTask] = []
        
        # 性能统计
        self.total_energy_consumed = 0.0
        self.total_tasks_processed = 0
        self.total_tasks_dropped = 0
        self.total_penalty = 0.0
        
        # 资源状态
        self.available_cpu = 1.0  # 可用CPU比例
        # 初始带宽设置（将在update_bandwidth_from_communication中更新）
        # 使用配置中的初始值以避免None问题
        self.current_bandwidth = self.config['communication']['b_us_hz'] / 8e6  # MB/s
        
        # 最大并行任务数
        self.max_parallel = self.config['computation']['max_parallel_tasks']
        
        self.logger = logging.getLogger(f"Satellite_{satellite_id}")
        
    def add_task(self, task: SatelliteTask) -> bool:
        """
        添加任务到队列
        
        Args:
            task: 任务对象
            
        Returns:
            是否成功添加
        """
        if len(self.task_queue) >= self.scheduler.max_queue_size:
            self.logger.warning(f"Queue full, dropping task {task.task_id}")
            self.drop_task(task, reason="queue_full")
            return False
        
        self.task_queue.append(task)
        self.logger.info(f"Added task {task.task_id} to queue")
        return True
    
    def drop_task(self, task: SatelliteTask, reason: str = "timeout"):
        """
        丢弃任务
        
        Args:
            task: 任务对象
            reason: 丢弃原因
        """
        task.status = TaskStatus.DROPPED
        self.dropped_tasks.append(task)
        self.total_tasks_dropped += 1
        self.total_penalty += task.drop_penalty
        
        self.logger.info(f"Dropped task {task.task_id}, reason: {reason}, penalty: {task.drop_penalty}")
    
    def process_task_computation(self, task: SatelliteTask, time_delta: float) -> Dict[str, Any]:
        """
        实际执行任务计算
        
        Args:
            task: 任务对象
            time_delta: 处理时间段
            
        Returns:
            任务处理结果
        """
        # 计算本时间段内可处理的CPU周期
        effective_cpu = self.scheduler.f_sat * task.allocated_cpu
        cycles_processed = effective_cpu * time_delta
        
        # 限制不超过剩余复杂度
        if hasattr(task, 'remaining_complexity'):
            cycles_processed = min(cycles_processed, task.remaining_complexity)
        else:
            cycles_processed = min(cycles_processed, task.complexity)
        
        # 模拟计算结果生成
        result_size_mb = task.data_size * 0.1  # 假设结果大小是输入的 10%
        
        # 计算处理进度
        progress_delta = cycles_processed / task.complexity if task.complexity > 0 else 0
        
        return {
            'cycles_processed': cycles_processed,
            'progress_delta': progress_delta,
            'result_size_mb': result_size_mb,
            'energy_consumed': self.calculate_energy_consumption(task, cycles_processed),
            'processing_time': time_delta
        }
    
    def schedule_tasks(self, current_time: float) -> List[SatelliteTask]:
        """
        执行任务调度
        
        Args:
            current_time: 当前时间（秒）
            
        Returns:
            被调度的任务列表
        """
        # 检查并移除超时任务
        self.check_timeouts(current_time)
        
        if not self.task_queue:
            return []
        
        # 优化：使用缓存和增量更新
        task_scores = []
        for task in self.task_queue:
            # 检查缓存
            cache_key = f"{task.task_id}_{current_time}"
            if cache_key in self.task_priority_cache:
                score = self.task_priority_cache[cache_key]
            else:
                score = self.scheduler.calculate_priority_score(
                    task, current_time, self.current_bandwidth
                )
                self.task_priority_cache[cache_key] = score
            task_scores.append((score, task))
        
        # 使用堆排序优化
        # 只获取前N个最高分数的任务
        available_slots = min(
            self.max_parallel - len(self.current_tasks),
            len(task_scores)
        )
        
        if available_slots <= 0:
            return []
        
        # 使用nlargest获取前N个最高分数
        top_tasks = heapq.nlargest(available_slots * 2, task_scores, key=lambda x: x[0])  # 获取2倍以备选
        
        scheduled_tasks = []
        remaining_cpu = self.available_cpu  # 跟踪剩余可用CPU
        
        # 选择得分最高的可行任务
        for score, task in top_tasks:
            if len(scheduled_tasks) >= available_slots or remaining_cpu <= 0:
                break
                
            # 更智能的CPU分配策略：基于任务优先级和紧急度
            # 优先级更高的任务获得更多CPU资源
            num_waiting = available_slots - len(scheduled_tasks)
            if num_waiting > 0 and remaining_cpu > 0:
                # 计算权重因子
                urgency_factor = 1.0 / (task.deadline - current_time + self.scheduler.epsilon_urgency)
                priority_factor = task.priority / 5.0  # 假设最大优先级为5
                weight = (priority_factor + urgency_factor) / 2.0
                
                # 基于权重分配CPU，使用剩余CPU而不是总可用CPU
                base_allocation = remaining_cpu / num_waiting
                cpu_allocation = min(base_allocation * (1.0 + weight * 0.5), remaining_cpu)
                cpu_allocation = max(cpu_allocation, self.config['computation']['min_cpu_allocation'] / 100.0)
            else:
                cpu_allocation = remaining_cpu
            
            # 检查可行性
            if self.scheduler.check_feasibility(task, current_time, cpu_allocation, self.current_bandwidth):
                # 从队列中移除
                self.task_queue.remove(task)
                
                # 设置任务参数
                task.allocated_cpu = cpu_allocation
                task.start_time = current_time
                task.status = TaskStatus.PROCESSING
                
                # 估算处理时间（统一使用秒为单位）
                total_time, comm_time, comp_time = self.scheduler.estimate_processing_time(
                    task, cpu_allocation, self.current_bandwidth
                )
                task.processing_delay = comp_time  # 秒
                task.communication_delay = comm_time  # 秒
                task.total_delay = total_time  # 秒
                task.completion_time = current_time + total_time  # 秒
                
                # 初始化跨时隙处理字段
                task.remaining_complexity = task.complexity
                task.last_update_time = current_time
                
                # 计算能耗
                task.energy_consumed = self.calculate_energy_consumption(task)
                
                # 添加到当前处理列表
                self.current_tasks.append(task)
                scheduled_tasks.append(task)
                
                # 更新剩余CPU资源（用于本轮调度）
                remaining_cpu -= task.allocated_cpu
                # 更新实际可用CPU资源
                self.available_cpu -= task.allocated_cpu
                
                self.logger.info(f"Scheduled task {task.task_id}, score: {score:.2f}, "
                               f"estimated completion: {task.completion_time:.2f}")
            else:
                # 任务不可行，丢弃
                self.task_queue.remove(task)
                self.drop_task(task, reason="infeasible")
        
        return scheduled_tasks
    
    def process_tasks(self, current_time: float) -> List[SatelliteTask]:
        """
        处理任务（支持跨时隙处理）
        
        Args:
            current_time: 当前时间（秒）
            
        Returns:
            完成的任务列表
        """
        completed = []
        
        # 检查当前处理中的任务
        remaining_tasks = []
        for task in self.current_tasks:
            # 更新任务处理进度
            self.update_task_progress(task, current_time)
            
            if task.processing_progress >= 1.0:
                # 任务完成
                task.status = TaskStatus.COMPLETED
                task.completion_time = current_time
                self.completed_tasks.append(task)
                completed.append(task)
                
                # 更新统计
                self.total_tasks_processed += 1
                self.total_energy_consumed += task.accumulated_energy
                
                # 释放CPU资源
                self.available_cpu = min(1.0, self.available_cpu + task.allocated_cpu)
                
                self.logger.info(f"Completed task {task.task_id}, "
                               f"progress: {task.processing_progress:.1%}, "
                               f"total_time: {task.accumulated_processing_time:.2f}s, "
                               f"energy: {task.accumulated_energy:.2e}J")
            else:
                remaining_tasks.append(task)
                self.logger.debug(f"Task {task.task_id} progress: {task.processing_progress:.1%}")
        
        self.current_tasks = remaining_tasks
        
        return completed
    
    def check_timeouts(self, current_time: float):
        """
        检查并处理超时任务
        
        Args:
            current_time: 当前时间（秒）
        """
        timeout_tasks = []
        for task in self.task_queue:
            if task.deadline <= current_time:
                timeout_tasks.append(task)
        
        for task in timeout_tasks:
            self.task_queue.remove(task)
            self.drop_task(task, reason="timeout")
    
    def calculate_energy_consumption(self, task: SatelliteTask, processing_cycles: Optional[float] = None) -> float:
        """
        计算任务能耗
        
        Args:
            task: 任务对象
            processing_cycles: 处理的CPU周期数（用于部分处理）
            
        Returns:
            能耗（焦耳）
        """
        # E = zeta * C
        # 修复：正确处理跨时隙任务的能耗计算
        if processing_cycles is None:
            # 如果是跨时隙任务，计算已处理的周期数
            if hasattr(task, 'remaining_complexity') and task.remaining_complexity is not None:
                processing_cycles = task.complexity - task.remaining_complexity
            else:
                processing_cycles = task.complexity
        
        energy = self.scheduler.zeta_leo * processing_cycles
        
        return energy
    
    def update_resources(self, available_cpu: float, bandwidth: Optional[float] = None):
        """
        更新资源状态
        
        Args:
            available_cpu: 可用CPU比例（必须提供）
            bandwidth: 可用带宽（MB/s）
        """
        self.available_cpu = available_cpu
        if bandwidth is not None:
            self.current_bandwidth = bandwidth
    
    def update_bandwidth_from_communication(self, time_step: int, ground_station_id: int) -> float:
        """
        从通信管理器获取真实带宽
        
        Args:
            time_step: 当前时隙
            ground_station_id: 地面站ID
            
        Returns:
            实际可用带宽（MB/s）
        """
        if self.comm_manager is None:
            # 如果没有通信管理器，使用配置中的值
            self.current_bandwidth = self.config['communication']['b_us_hz'] / 8e6
            return self.current_bandwidth
        
        # 获取卫星-地面通信矩阵
        comm_matrix = self.comm_manager.get_satellite_ground_communication_matrix(time_step)
        
        # 获取卫星和地面站索引
        sat_ids = comm_matrix['satellite_ids']
        ground_ids = comm_matrix['ground_station_ids']
        
        try:
            sat_idx = sat_ids.index(self.satellite_id)
            ground_idx = ground_ids.index(ground_station_id)
            
            # 检查可见性
            if comm_matrix['visibility'][sat_idx, ground_idx]:
                # 获取上行数据速率（bps），转换为MB/s
                uplink_rate_bps = comm_matrix['uplink_data_rate_bps'][sat_idx, ground_idx]
                self.current_bandwidth = uplink_rate_bps / 8e6
            else:
                self.current_bandwidth = 0.0
        except (ValueError, IndexError):
            self.logger.warning(f"Failed to get bandwidth for satellite {self.satellite_id} and ground station {ground_station_id}")
            self.current_bandwidth = 0.0
        
        return self.current_bandwidth
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        return {
            'satellite_id': self.satellite_id,
            'total_tasks_processed': self.total_tasks_processed,
            'total_tasks_dropped': self.total_tasks_dropped,
            'total_penalty': self.total_penalty,
            'total_energy_consumed': self.total_energy_consumed,
            'queue_length': len(self.task_queue),
            'processing_tasks': len(self.current_tasks),
            'completed_tasks': len(self.completed_tasks),
            'dropped_tasks': len(self.dropped_tasks),
            'available_cpu': self.available_cpu,
            'current_bandwidth': self.current_bandwidth
        }
    
    def update_task_progress(self, task: SatelliteTask, current_time: float):
        """
        更新任务处理进度（支持跨时隙）
        
        Args:
            task: 任务对象
            current_time: 当前时间（秒）
        """
        # 初始化跨时隙处理字段（应该在任务开始时完成）
        if not hasattr(task, 'remaining_complexity') or task.remaining_complexity is None:
            task.remaining_complexity = task.complexity
        if task.last_update_time is None:
            task.last_update_time = task.start_time if task.start_time else current_time
        
        # 计算自上次更新以来的时间差
        time_delta = current_time - task.last_update_time
        
        if time_delta > 0:
            # 使用新的处理计算方法
            result = self.process_task_computation(task, time_delta)
            
            # 更新任务状态
            task.remaining_complexity -= result['cycles_processed']
            task.processing_progress = 1.0 - (task.remaining_complexity / task.complexity) if task.complexity > 0 else 1.0
            
            # 累计处理时间和能量
            task.accumulated_processing_time += result['processing_time']
            task.accumulated_energy += result['energy_consumed']
            task.energy_consumed = task.accumulated_energy
            
            # 更新最后更新时间
            task.last_update_time = current_time
    
    def handle_timeslot_boundary(self, current_time: float):
        """
        处理时隙边界（保存任务状态）
        
        Args:
            current_time: 当前时间（秒）
        """
        self.logger.info(f"Handling timeslot boundary at time {current_time}")
        
        # 更新所有正在处理的任务的进度
        for task in self.current_tasks:
            self.update_task_progress(task, current_time)
            self.logger.debug(f"Task {task.task_id} saved with progress {task.processing_progress:.1%}")
    
    def restore_tasks_from_previous_timeslot(self, tasks: List[SatelliteTask]):
        """
        恢复上一时隙未完成的任务
        
        Args:
            tasks: 需要恢复的任务列表
        """
        for task in tasks:
            if task.processing_progress < 1.0:
                # 将任务重新加入处理队列
                self.current_tasks.append(task)
                # 重新分配CPU资源
                self.available_cpu -= task.allocated_cpu
                self.logger.info(f"Restored task {task.task_id} with progress {task.processing_progress:.1%}")
    
    def process_task_segment(self, task_record: TaskTrackingRecord, 
                            segment_ratio: float,
                            current_time: float) -> Optional[ProcessingNode]:
        """
        处理任务分片（与task_tracking集成）
        
        Args:
            task_record: 任务跟踪记录
            segment_ratio: 处理比例（必须提供）
            current_time: 当前时间（秒，必须提供）
            
        Returns:
            处理节点信息
        """
        # 创建SatelliteTask
        # 简化复杂度计算
        data_size_bits = task_record.data_size_mb * self.config['communication']['mb_to_bits']
        task_complexity = task_record.complexity_cycles_per_bit * data_size_bits * segment_ratio
        
        task = SatelliteTask(
            task_id=f"task_{task_record.task_id}_seg_{len(task_record.processing_nodes)}",
            priority=task_record.priority,
            deadline=task_record.deadline_timestamp,  # 已经是秒为单位
            data_size=task_record.data_size_mb * segment_ratio,
            complexity=task_complexity,
            drop_penalty=self.config['computation']['default_drop_penalty'] * task_record.priority,
            arrival_time=current_time,  # 秒
            is_partial_processing=(segment_ratio < 1.0),
            segment_ratio=segment_ratio
        )
        
        # 初始化跨时隙处理字段
        task.remaining_complexity = task.complexity
        task.accumulated_processing_time = 0.0
        task.accumulated_energy = 0.0
        
        # 添加到队列并调度
        if self.add_task(task):
            scheduled = self.schedule_tasks(current_time)
            if task in scheduled:
                # 创建处理节点记录
                node = ProcessingNode(
                    node_type=NodeType.SATELLITE,
                    node_id=self.satellite_id,
                    processing_percentage=0.0,
                    start_time=task.start_time,
                    end_time=task.completion_time if task.completion_time else current_time,
                    task_segment_id=task.task_id,
                    segment_ratio=segment_ratio,
                    is_partial_processing=(segment_ratio < 1.0),
                    processing_time=task.total_delay,  # 秒
                    energy_consumption=task.energy_consumed,
                    cpu_cycles_used=task.complexity,
                    memory_usage_mb=task.data_size
                )
                return node
        
        return None
    
    def reset(self):
        """重置卫星状态"""
        self.task_queue.clear()
        self.current_tasks.clear()
        self.completed_tasks.clear()
        self.dropped_tasks.clear()
        
        self.total_energy_consumed = 0.0
        self.total_tasks_processed = 0
        self.total_tasks_dropped = 0
        self.total_penalty = 0.0
        
        self.available_cpu = 1.0
        # 清空缓存
        self.task_priority_cache.clear()
        # 重置带宽为初始值
        self.current_bandwidth = self.config['communication']['b_us_hz'] / 8e6