import numpy as np
import math
import logging
from typing import Dict, <PERSON><PERSON>, Optional
from pathlib import Path
import yaml

# Use absolute imports as per coding standard #1
from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.Foundation_Layer.time_manager import TimeManager


class CommunicationManager:
    """通信链路管理器 - 计算卫星网络中的通信链路状态"""
    
    def __init__(self, orbital_updater: OrbitalUpdater = None, 
                 config_file: str = None,
                 time_manager: Optional[TimeManager] = None):
        """
        初始化通信管理器
        
        Args:
            orbital_updater: 轨道更新器实例
            config_file: 配置文件路径
            time_manager: 时间管理器实例
        """
        # 设置配置文件路径
        current_dir = Path(__file__).parent
        self.config_file = config_file or str(current_dir / "config.yaml")
        self.config = self._load_config()
        
        # 初始化轨道更新器
        if orbital_updater is None:
            # Get data file path
            env_dir = Path(__file__).parent.parent  # Go up to env directory
            data_file = str(env_dir / "env_data" / "satellite_data72_1.csv")
            
            self.orbital_updater = OrbitalUpdater(
                data_file=data_file,
                config_file=self.config_file,
                time_manager=time_manager
            )
        else:
            self.orbital_updater = orbital_updater
            
        # 加载通信参数
        self._load_communication_parameters()
        
        # 缓存机制
        self._communication_cache = {}
        
        logging.info("Communication Manager initialized successfully")
    
    def _load_config(self) -> dict:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logging.critical(f"Failed to load config file: {self.config_file} - {e}")
            raise
    
    def _load_communication_parameters(self):
        """从配置文件加载通信参数"""
        comm_config = self.config['communication']
        
        # 射频通信参数
        self.rf_noise_power_w = comm_config['rf_noise_power_w']
        self.rf_carrier_freq_hz = comm_config['rf_carrier_freq_hz']
        self.rician_k_factor = comm_config['rician_k_factor']
        
        # 用户-卫星上行链路
        self.b_us_hz = comm_config['b_us_hz']
        self.p_u_w = comm_config['p_u_w']
        
        # 卫星-用户下行链路
        self.b_su_hz = comm_config['b_su_hz']
        self.p_su_w = comm_config['p_su_w']
        
        # 卫星-云下行链路
        self.b_sc_hz = comm_config['b_sc_hz']
        self.p_sc_w = comm_config['p_sc_w']
        
        # 云-卫星上行链路
        self.b_cs_hz = comm_config['b_cs_hz']
        self.p_c_w = comm_config['p_c_w']
        
        # 星间激光链路
        self.isl_data_rate_bps = comm_config['isl_tra_rate']
        
        # 物理常数
        self.light_speed_ms = comm_config['light_speed_ms']
        
        # 天线和损耗参数
        self.antenna_gain_db = comm_config['antenna_gain_db']
        self.system_noise_dbm_hz = comm_config['system_noise_dbm_hz']
        self.implementation_loss_db = comm_config['implementation_loss_db']
        self.rain_fade_margin_db = comm_config['rain_fade_margin_db']
        self.coding_efficiency = comm_config['coding_efficiency']
        
        # 协议参数
        self.processing_delay_ms = comm_config['processing_delay_ms']
        
    def _w_to_dbm(self, power_w: float) -> float:
        """将功率从瓦特转换为dBm"""
        if power_w <= 0:
            return -float('inf')
        return 10 * np.log10(power_w * 1000)
    
    def _dbm_to_w(self, power_dbm: float) -> float:
        """将功率从dBm转换为瓦特"""
        return 10 ** ((power_dbm - 30) / 10)
    
    def calculate_path_loss(self, distance_km: np.ndarray, frequency_hz: float) -> np.ndarray:
        """
        计算自由空间路径损耗 (FSPL)
        
        Args:
            distance_km: 距离矩阵 (km)
            frequency_hz: 载波频率 (Hz)
            
        Returns:
            路径损耗矩阵 (dB)
        """
        # 转换距离为米
        distance_m = distance_km * 1000
        
        # 避免log(0)的情况
        distance_m = np.maximum(distance_m, 1.0)
        
        # FSPL = 20*log10(d) + 20*log10(f) + 20*log10(4π/c)
        fspl_db = (20 * np.log10(distance_m) + 
                   20 * np.log10(frequency_hz) + 
                   20 * np.log10(4 * np.pi / self.light_speed_ms))
        
        return fspl_db
    
    def calculate_received_power(self, tx_power_w: float, distance_km: np.ndarray, 
                                frequency_hz: float, tx_gain_db: float = None,
                                rx_gain_db: float = None,
                                other_losses_db: float = 0) -> np.ndarray:
        """
        计算接收功率
        
        Args:
            tx_power_w: 发射功率 (W)
            distance_km: 距离矩阵 (km)
            frequency_hz: 载波频率 (Hz)
            tx_gain_db: 发射天线增益 (dB)
            rx_gain_db: 接收天线增益 (dB)
            other_losses_db: 其他损耗 (dB)
            
        Returns:
            接收功率矩阵 (dBm)
        """
        # 使用默认天线增益
        if tx_gain_db is None:
            tx_gain_db = self.antenna_gain_db
        if rx_gain_db is None:
            rx_gain_db = self.antenna_gain_db
            
        # 转换发射功率为dBm
        tx_power_dbm = self._w_to_dbm(tx_power_w)
        
        # 计算路径损耗
        path_loss_db = self.calculate_path_loss(distance_km, frequency_hz)
        
        # 计算总的其他损耗
        total_other_losses = other_losses_db + self.implementation_loss_db + self.rain_fade_margin_db
        
        # 链路预算: Pr = Pt + Gt + Gr - PL - L_other
        rx_power_dbm = tx_power_dbm + tx_gain_db + rx_gain_db - path_loss_db - total_other_losses
        
        return rx_power_dbm
    
    def calculate_snr(self, rx_power_dbm: np.ndarray, bandwidth_hz: float) -> np.ndarray:
        """
        计算信噪比 (SNR)
        
        Args:
            rx_power_dbm: 接收功率 (dBm)
            bandwidth_hz: 信道带宽 (Hz)
            
        Returns:
            信噪比矩阵 (dB)
        """
        # 计算噪声功率
        noise_power_dbm = self.system_noise_dbm_hz + 10 * np.log10(bandwidth_hz)
        
        # SNR = 接收功率 - 噪声功率
        snr_db = rx_power_dbm - noise_power_dbm
        
        return snr_db
    
    def calculate_data_rate(self, snr_db: np.ndarray, bandwidth_hz: float) -> np.ndarray:
        """
        使用Shannon公式计算数据速率
        
        Args:
            snr_db: 信噪比 (dB)
            bandwidth_hz: 信道带宽 (Hz)
            
        Returns:
            数据速率矩阵 (bps)
        """
        # 转换SNR到线性值
        snr_linear = 10 ** (snr_db / 10)
        
        # Shannon容量: C = B * log2(1 + SNR)
        capacity_bps = bandwidth_hz * np.log2(1 + snr_linear)
        
        # 考虑编码效率
        actual_rate_bps = capacity_bps * self.coding_efficiency
        
        # 将负SNR对应的速率设为0
        actual_rate_bps[snr_db < 0] = 0
        
        return actual_rate_bps
    
    def get_isl_communication_matrix(self, time_step: int) -> Dict[str, np.ndarray]:
        """
        获取卫星间通信矩阵
        
        Args:
            time_step: 时间步
            
        Returns:
            包含数据速率、SNR、链路状态的字典
        """
        cache_key = f"isl_comm_{time_step}"
        if cache_key in self._communication_cache:
            return self._communication_cache[cache_key]
        
        # 获取卫星位置和可见性信息
        satellites = self.orbital_updater.get_satellites_at_time(time_step)
        visibility_matrix, distance_matrix = self.orbital_updater.build_visibility_matrix(satellites)
        
        n_satellites = len(satellites)
        
        # 对于星间激光链路，使用固定的高数据速率（不受距离影响，只要可见）
        data_rate_matrix = np.zeros_like(distance_matrix)
        
        # 可见的链路使用激光通信速率
        data_rate_matrix[visibility_matrix] = self.isl_data_rate_bps
        
        # ISL使用激光通信，不计算RF链路参数
        # 对于激光链路，SNR主要取决于可见性，可见时SNR高，不可见时无链路
        snr_db = np.zeros_like(distance_matrix)
        snr_db[visibility_matrix] = np.inf  # 激光链路可见时视为理想链路
        
        # 计算传播延迟 (ms)
        propagation_delay_ms = (distance_matrix * 1000) / self.light_speed_ms * 1000
        
        result = {
            'data_rate_bps': data_rate_matrix,
            'snr_db': snr_db,
            'distance_km': distance_matrix,
            'visibility': visibility_matrix,
            'propagation_delay_ms': propagation_delay_ms,
            'link_type': 'laser',
            'satellite_ids': list(satellites.keys())
        }
        
        self._communication_cache[cache_key] = result
        return result
    
    def get_satellite_ground_communication_matrix(self, time_step: int) -> Dict[str, np.ndarray]:
        """
        获取卫星-地面站通信矩阵
        
        Args:
            time_step: 时间步
            
        Returns:
            包含上下行链路数据速率、SNR等信息的字典
        """
        cache_key = f"sat_ground_comm_{time_step}"
        if cache_key in self._communication_cache:
            return self._communication_cache[cache_key]
        
        # 获取卫星和可见性信息
        satellites = self.orbital_updater.get_satellites_at_time(time_step)
        visibility_matrix, distance_matrix = self.orbital_updater.build_satellite_ground_visibility_matrix(
            satellites, time_step
        )
        
        # 计算上行链路 (用户到卫星)
        uplink_rx_power_dbm = self.calculate_received_power(
            tx_power_w=self.p_u_w,
            distance_km=distance_matrix,
            frequency_hz=self.rf_carrier_freq_hz
        )
        uplink_snr_db = self.calculate_snr(uplink_rx_power_dbm, self.b_us_hz)
        uplink_data_rate_bps = self.calculate_data_rate(uplink_snr_db, self.b_us_hz)
        
        # 计算下行链路 (卫星到用户)
        downlink_rx_power_dbm = self.calculate_received_power(
            tx_power_w=self.p_su_w,
            distance_km=distance_matrix,
            frequency_hz=self.rf_carrier_freq_hz
        )
        downlink_snr_db = self.calculate_snr(downlink_rx_power_dbm, self.b_su_hz)
        downlink_data_rate_bps = self.calculate_data_rate(downlink_snr_db, self.b_su_hz)
        
        # 不可见的链路速率设为0
        uplink_data_rate_bps[~visibility_matrix] = 0
        downlink_data_rate_bps[~visibility_matrix] = 0
        
        # 计算传播延迟 (ms)
        propagation_delay_ms = (distance_matrix * 1000) / self.light_speed_ms * 1000
        
        result = {
            'uplink_data_rate_bps': uplink_data_rate_bps,
            'uplink_snr_db': uplink_snr_db,
            'downlink_data_rate_bps': downlink_data_rate_bps,
            'downlink_snr_db': downlink_snr_db,
            'distance_km': distance_matrix,
            'visibility': visibility_matrix,
            'propagation_delay_ms': propagation_delay_ms,
            'link_type': 'rf',
            'satellite_ids': list(satellites.keys()),
            'ground_station_ids': list(self.orbital_updater.ground_stations.keys())
        }
        
        self._communication_cache[cache_key] = result
        return result
    
    def get_satellite_cloud_communication_matrix(self, time_step: int) -> Dict[str, np.ndarray]:
        """
        获取卫星-云中心通信矩阵
        
        Args:
            time_step: 时间步
            
        Returns:
            包含上下行链路数据速率、SNR等信息的字典
        """
        cache_key = f"sat_cloud_comm_{time_step}"
        if cache_key in self._communication_cache:
            return self._communication_cache[cache_key]
        
        # 获取卫星和可见性信息
        satellites = self.orbital_updater.get_satellites_at_time(time_step)
        visibility_matrix, distance_matrix = self.orbital_updater.build_satellite_cloud_visibility_matrix(
            satellites, time_step
        )
        
        # 计算上行链路 (云到卫星)
        uplink_rx_power_dbm = self.calculate_received_power(
            tx_power_w=self.p_c_w,
            distance_km=distance_matrix,
            frequency_hz=self.rf_carrier_freq_hz
        )
        uplink_snr_db = self.calculate_snr(uplink_rx_power_dbm, self.b_cs_hz)
        uplink_data_rate_bps = self.calculate_data_rate(uplink_snr_db, self.b_cs_hz)
        
        # 计算下行链路 (卫星到云)
        downlink_rx_power_dbm = self.calculate_received_power(
            tx_power_w=self.p_sc_w,
            distance_km=distance_matrix,
            frequency_hz=self.rf_carrier_freq_hz
        )
        downlink_snr_db = self.calculate_snr(downlink_rx_power_dbm, self.b_sc_hz)
        downlink_data_rate_bps = self.calculate_data_rate(downlink_snr_db, self.b_sc_hz)
        
        # 不可见的链路速率设为0
        uplink_data_rate_bps[~visibility_matrix] = 0
        downlink_data_rate_bps[~visibility_matrix] = 0
        
        # 计算传播延迟 (ms)
        propagation_delay_ms = (distance_matrix * 1000) / self.light_speed_ms * 1000
        
        result = {
            'uplink_data_rate_bps': uplink_data_rate_bps,
            'uplink_snr_db': uplink_snr_db,
            'downlink_data_rate_bps': downlink_data_rate_bps,
            'downlink_snr_db': downlink_snr_db,
            'distance_km': distance_matrix,
            'visibility': visibility_matrix,
            'propagation_delay_ms': propagation_delay_ms,
            'link_type': 'rf',
            'satellite_ids': list(satellites.keys()),
            'cloud_station_ids': list(self.orbital_updater.cloud_stations.keys())
        }
        
        self._communication_cache[cache_key] = result
        return result
    
    def get_link_quality_metrics(self, time_step: int) -> Dict[str, Dict]:
        """
        获取所有链路的质量指标汇总
        
        Args:
            time_step: 时间步
            
        Returns:
            包含所有链路类型质量指标的字典
        """
        isl_comm = self.get_isl_communication_matrix(time_step)
        sat_ground_comm = self.get_satellite_ground_communication_matrix(time_step)
        sat_cloud_comm = self.get_satellite_cloud_communication_matrix(time_step)
        
        metrics = {
            'isl': {
                'avg_data_rate_gbps': np.mean(isl_comm['data_rate_bps'][isl_comm['visibility']]) / 1e9 
                                      if np.any(isl_comm['visibility']) else 0,
                'total_links': np.sum(isl_comm['visibility']),
                'avg_delay_ms': np.mean(isl_comm['propagation_delay_ms'][isl_comm['visibility']])
                               if np.any(isl_comm['visibility']) else 0
            },
            'satellite_ground': {
                'avg_uplink_rate_mbps': np.mean(sat_ground_comm['uplink_data_rate_bps'][sat_ground_comm['visibility']]) / 1e6
                                        if np.any(sat_ground_comm['visibility']) else 0,
                'avg_downlink_rate_mbps': np.mean(sat_ground_comm['downlink_data_rate_bps'][sat_ground_comm['visibility']]) / 1e6
                                          if np.any(sat_ground_comm['visibility']) else 0,
                'total_links': np.sum(sat_ground_comm['visibility']),
                'avg_delay_ms': np.mean(sat_ground_comm['propagation_delay_ms'][sat_ground_comm['visibility']])
                               if np.any(sat_ground_comm['visibility']) else 0
            },
            'satellite_cloud': {
                'avg_uplink_rate_mbps': np.mean(sat_cloud_comm['uplink_data_rate_bps'][sat_cloud_comm['visibility']]) / 1e6
                                        if np.any(sat_cloud_comm['visibility']) else 0,
                'avg_downlink_rate_mbps': np.mean(sat_cloud_comm['downlink_data_rate_bps'][sat_cloud_comm['visibility']]) / 1e6
                                          if np.any(sat_cloud_comm['visibility']) else 0,
                'total_links': np.sum(sat_cloud_comm['visibility']),
                'avg_delay_ms': np.mean(sat_cloud_comm['propagation_delay_ms'][sat_cloud_comm['visibility']])
                               if np.any(sat_cloud_comm['visibility']) else 0
            }
        }
        
        return metrics
    
    def clear_cache(self):
        """清除缓存"""
        self._communication_cache.clear()
        logging.info("Communication cache cleared")