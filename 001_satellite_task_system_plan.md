# 科研仿真平台功能开发规划 - SPACE2卫星任务系统

-----

## **模块/功能编号:** M001

## **模块/功能名称:** 卫星边缘计算任务分发与处理系统

### **1. 目标与范围定义 (Objective & Scope Definition)**

* **核心目标 / 待解决的科研问题 (Core Objective / Research Problem to Solve):**

  > 构建一个完整的LEO卫星边缘计算任务处理系统，实现从任务生成、分发到处理的全流程仿真。支持基于距离的任务分配策略，并提供详细的延迟和能耗指标记录，为算法性能评估提供基准对比。

* **功能概述 (Feature Overview):**

  > 该系统模拟72颗LEO卫星处理来自420个地面用户终端的计算任务。系统将实现任务生成、最近卫星分配、任务处理和结果返回的完整流程，并记录各阶段的关键性能指标。

* **关键输入 (Key Inputs):**

  - 配置文件: `src/env/physics_layer/config.yaml`
  - 卫星轨道数据: `src/env/env_data/satellite_data72_1.csv`
  - 地面站位置: `src/env/env_data/global_ground_stations.csv`
  - 时隙信息: 1-1441时隙，每时隙5秒

* **关键输出 (Key Outputs):**

  - 任务处理结果（完成/失败状态）
  - 端到端延迟指标（通信延迟、排队延迟、处理延迟）
  - 能耗指标（传输能耗、计算能耗）
  - 任务完成率统计
  - JSON格式的详细日志文件

* **边界与约束 (Boundaries & Constraints):**

  > - 仅实现基于距离的最近卫星分配策略
  > - 不包含星间链路通信
  > - 不包含云协同处理
  > - 任务处理采用FIFO队列，无优化调度
  > - 所有任务必须在截止时间前完成，否则标记为失败

### **2. 技术与交互设计 (Technical & Interaction Design)**

* **设计思路 / 算法原理 (Design Rationale / Algorithm Principle):**

  ```
  任务处理流程:
  1. 任务生成阶段（每时隙）
     - 根据地面站位置和类型生成任务
     - 任务包含：数据大小、计算复杂度、截止时间、优先级
  
  2. 任务分配阶段
     - 计算所有可见卫星与地面站的距离
     - 选择距离最近的可见卫星
     - 将任务加入卫星任务队列
  
  3. 任务处理阶段
     - 卫星按FIFO顺序处理任务
     - 计算处理时间 = 数据传输时间 + 计算时间
     - 更新任务状态和性能指标
  
  4. 指标记录阶段
     - 通信延迟 = 数据大小 / 带宽 + 传播延迟
     - 处理延迟 = 任务复杂度 / CPU频率
     - 能耗 = 传输能耗 + 计算能耗
  ```

* **模块交互设计 (Module Interaction Design):**

  * **影响的现有模块 (Affected Existing Modules):** 
    - `src/env/task/task_generator.py` - 增强任务生成功能
    - `src/env/satellite_cloud/satellite.py` - 简化为基础处理
    - `src/env/task/task_tracking.py` - 完整任务跟踪

  * **新增的模块/类 (New Modules/Classes):** 
    - `src/agent/local/run_local.py` - 本地运行主程序
    - `src/agent/local/task_distributor.py` - 任务分发器
    - `src/agent/local/metrics_recorder.py` - 指标记录器

  * **数据交互接口 (Data Interaction Interface):**
    > - `LocalRunner.run_simulation()`: 主仿真循环，每时隙调用各组件
    > - `TaskDistributor.assign_tasks()`: 接收任务列表和可见性矩阵，返回分配结果
    > - `Satellite.process_tasks()`: 处理队列中的任务，返回处理结果
    > - `MetricsRecorder.record()`: 记录任务处理的各项指标

* **数据结构与状态变量 (Data Structures & State Variables):**

  ```python
  # 任务分配记录
  @dataclass
  class TaskAssignment:
      task_id: str
      ground_station_id: int
      satellite_id: str
      distance_km: float
      assignment_time: float
      visibility_score: float
  
  # 处理结果记录
  @dataclass
  class ProcessingResult:
      task_id: str
      status: str  # "completed", "failed", "timeout"
      start_time: float
      end_time: float
      communication_delay: float
      queuing_delay: float
      processing_delay: float
      total_delay: float
      transmission_energy: float
      computation_energy: float
      total_energy: float
  
  # 时隙统计
  @dataclass
  class TimeslotMetrics:
      timeslot: int
      tasks_generated: int
      tasks_assigned: int
      tasks_completed: int
      tasks_failed: int
      avg_delay: float
      avg_energy: float
      satellite_utilization: Dict[str, float]
  ```

### **3. 验证与评估方案 (Validation & Evaluation Plan)**

* **正确性验证方案 (Correctness Validation Plan):**

  * **单元验证 (Unit Validation):**
    > - 测试任务生成器：验证不同地面站类型的任务生成率
    > - 测试距离计算：使用已知坐标验证ECEF距离计算准确性
    > - 测试任务分配：验证最近卫星选择逻辑
    > - 测试延迟计算：验证各类延迟计算公式

  * **集成验证 (Integration Validation):**
    > - 运行10个时隙的短仿真，跟踪特定任务的完整生命周期
    > - 验证任务从生成到完成的状态转换
    > - 检查指标记录的完整性和一致性
    > - 验证卫星资源利用率计算

* **效果评估方案 (Impact Evaluation Plan):**

  * **对比实验设计 (Comparative Experiment Design):**
    > 设置三组实验：
    > - A组：本地处理（所有任务在地面处理，无传输）
    > - B组：最近卫星分配（基于距离的简单策略）
    > - C组：随机卫星分配（作为对照）
    > 
    > 比较指标：
    > - 平均任务完成率
    > - 平均端到端延迟
    > - 总能耗
    > - 卫星资源利用率方差

  * **期望结果与分析 (Expected Outcome & Analysis):**
    > - 本地处理（A组）：最低延迟，但无法利用卫星资源
    > - 最近卫星（B组）：延迟和能耗平衡，资源利用率中等
    > - 随机分配（C组）：延迟和能耗最高，资源利用不均
    > 
    > 预期B组相比C组能够降低20-30%的平均延迟，证明距离感知分配的有效性

-----

## **实施路线图**

### **阶段1：基础组件实现（第1-2天）**
1. 完善TaskGenerator，支持基于地面站类型的任务生成
2. 实现TaskDistributor，完成最近卫星分配逻辑
3. 简化Satellite类，实现FIFO任务处理

### **阶段2：指标系统构建（第3-4天）**
1. 实现MetricsRecorder，记录所有性能指标
2. 添加延迟计算模块（通信、排队、处理）
3. 添加能耗计算模块（传输、计算）

### **阶段3：本地运行程序（第5-6天）**
1. 实现LocalRunner主程序框架
2. 集成所有组件，实现完整仿真流程
3. 添加JSON格式的结果输出

### **阶段4：测试与验证（第7天）**
1. 编写单元测试，验证各组件功能
2. 运行集成测试，验证端到端流程
3. 执行对比实验，分析结果

## **关键技术细节**

### **距离计算公式（ECEF坐标系）**
```python
def calculate_distance(pos1_ecef, pos2_ecef):
    """计算两点间的欧氏距离"""
    return np.linalg.norm(pos1_ecef - pos2_ecef)
```

### **延迟计算公式**
```python
# 传播延迟
propagation_delay = distance / speed_of_light

# 传输延迟
transmission_delay = data_size_bits / bandwidth_bps

# 处理延迟
processing_delay = task_complexity_cycles / cpu_frequency_hz

# 总延迟
total_delay = propagation_delay + transmission_delay + queuing_delay + processing_delay
```

### **能耗计算公式**
```python
# 传输能耗
transmission_energy = transmit_power_w * transmission_time_s

# 计算能耗
computation_energy = cpu_power_per_cycle * task_complexity_cycles
```

## **文件组织结构**

```
src/
├── agent/
│   └── local/
│       ├── __init__.py
│       ├── run_local.py          # 主运行程序
│       ├── task_distributor.py   # 任务分发器
│       └── metrics_recorder.py   # 指标记录器
└── env/
    └── task/
        ├── task_generator.py      # 已有，需增强
        └── task_tracking.py       # 已有，需集成

test/
└── agent/
    └── local/
        ├── test_task_distributor.py
        ├── test_metrics_recorder.py
        └── test_integration.py
```

## **配置参数使用**

所有参数必须从`config.yaml`读取，包括：
- 系统参数：卫星数量、时隙数、时隙持续时间
- 通信参数：带宽、传输功率、载波频率
- 计算参数：CPU频率、能效系数
- 任务参数：数据大小范围、复杂度范围、截止时间范围

## **输出格式规范**

```json
{
  "simulation_info": {
    "total_timeslots": 1441,
    "total_satellites": 72,
    "total_ground_stations": 420
  },
  "timeslot_results": [
    {
      "timeslot": 1,
      "tasks_generated": 35,
      "tasks_completed": 28,
      "avg_delay_ms": 125.3,
      "avg_energy_j": 0.045
    }
  ],
  "final_statistics": {
    "total_tasks": 50000,
    "completion_rate": 0.92,
    "avg_delay_ms": 132.7,
    "total_energy_j": 2250.5
  }
}
```

-----

## **风险与缓解措施**

1. **性能风险**：大规模矩阵运算可能导致性能瓶颈
   - 缓解：使用NumPy向量化操作，避免Python循环

2. **内存风险**：存储所有任务记录可能占用大量内存
   - 缓解：定期将结果写入文件，只保留必要的统计信息

3. **精度风险**：浮点运算累积误差
   - 缓解：使用适当的数值精度，关键计算使用双精度

## **成功标准**

1. 系统能够完整运行1441个时隙的仿真
2. 任务完成率达到85%以上
3. 所有指标记录完整且一致
4. 输出结果可用于后续算法对比分析